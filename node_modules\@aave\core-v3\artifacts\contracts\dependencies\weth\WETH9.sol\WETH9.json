{"_format": "hh-sol-artifact-1", "contractName": "WETH9", "sourceName": "contracts/dependencies/weth/WETH9.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "src", "type": "address"}, {"indexed": true, "internalType": "address", "name": "guy", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "wad", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "dst", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "wad", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "src", "type": "address"}, {"indexed": true, "internalType": "address", "name": "dst", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "wad", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "src", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "wad", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON>", "type": "event"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "guy", "type": "address"}, {"internalType": "uint256", "name": "wad", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "deposit", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "dst", "type": "address"}, {"internalType": "uint256", "name": "wad", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "src", "type": "address"}, {"internalType": "address", "name": "dst", "type": "address"}, {"internalType": "uint256", "name": "wad", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "wad", "type": "uint256"}], "name": "withdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}