// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

interface IDODOFlashloan {
    function flashLoan(
        uint256 baseAmount,
        uint256 quoteAmount,
        address assetTo,
        bytes calldata data
    ) external;

    function swap(
        address fromToken,
        address toToken,
        uint256 fromAmount,
        uint256 minReturnAmount,
        address to
    ) external returns (uint256 returnAmount);
}
