{"_format": "hh-sol-artifact-1", "contractName": "IsolationModeLogic", "sourceName": "contracts/protocol/libraries/logic/IsolationModeLogic.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "asset", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "totalDebt", "type": "uint256"}], "name": "IsolationModeTotalDebtUpdated", "type": "event"}], "bytecode": "0x60566037600b82828239805160001a607314602a57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220d2215214a28d82a7052ab556d4e217d1af346b5c73c03fd2d835b0da1773a2ab64736f6c634300080a0033", "deployedBytecode": "0x73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220d2215214a28d82a7052ab556d4e217d1af346b5c73c03fd2d835b0da1773a2ab64736f6c634300080a0033", "linkReferences": {}, "deployedLinkReferences": {}}