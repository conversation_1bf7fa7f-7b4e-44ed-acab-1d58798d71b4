{"_format": "hh-sol-artifact-1", "contractName": "IPriceOracleSentinel", "sourceName": "contracts/interfaces/IPriceOracleSentinel.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newGracePeriod", "type": "uint256"}], "name": "GracePeriodUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "newSequencerOracle", "type": "address"}], "name": "SequencerOracleUpdated", "type": "event"}, {"inputs": [], "name": "ADDRESSES_PROVIDER", "outputs": [{"internalType": "contract IPoolAddressesProvider", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getGracePeriod", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getSequencerOracle", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "isBorrowAllowed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "isLiquidationAllowed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newGracePeriod", "type": "uint256"}], "name": "setGracePeriod", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newSequencerOracle", "type": "address"}], "name": "setSequencerOracle", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}