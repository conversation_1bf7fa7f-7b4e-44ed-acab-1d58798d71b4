{"_format": "hh-sol-artifact-1", "contractName": "IPoolAddressesProviderRegistry", "sourceName": "contracts/interfaces/IPoolAddressesProviderRegistry.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "addressesProvider", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}], "name": "AddressesProviderRegistered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "addressesProvider", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}], "name": "AddressesProviderUnregistered", "type": "event"}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "getAddressesProviderAddressById", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "addressesProvider", "type": "address"}], "name": "getAddressesProviderIdByAddress", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getAddressesProvidersList", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "provider", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "registerAddressesProvider", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "provider", "type": "address"}], "name": "unregisterAddressesProvider", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}