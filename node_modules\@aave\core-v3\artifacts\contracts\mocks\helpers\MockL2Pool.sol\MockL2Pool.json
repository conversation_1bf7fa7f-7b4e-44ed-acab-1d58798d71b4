{"_format": "hh-sol-artifact-1", "contractName": "MockL2Pool", "sourceName": "contracts/mocks/helpers/MockL2Pool.sol", "abi": [{"inputs": [{"internalType": "contract IPoolAddressesProvider", "name": "provider", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "reserve", "type": "address"}, {"indexed": true, "internalType": "address", "name": "backer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "fee", "type": "uint256"}], "name": "<PERSON><PERSON>nbacked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "reserve", "type": "address"}, {"indexed": false, "internalType": "address", "name": "user", "type": "address"}, {"indexed": true, "internalType": "address", "name": "onBehalfOf", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "enum DataTypes.InterestRateMode", "name": "interestRateMode", "type": "uint8"}, {"indexed": false, "internalType": "uint256", "name": "borrowRate", "type": "uint256"}, {"indexed": true, "internalType": "uint16", "name": "referralCode", "type": "uint16"}], "name": "Borrow", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "target", "type": "address"}, {"indexed": false, "internalType": "address", "name": "initiator", "type": "address"}, {"indexed": true, "internalType": "address", "name": "asset", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "enum DataTypes.InterestRateMode", "name": "interestRateMode", "type": "uint8"}, {"indexed": false, "internalType": "uint256", "name": "premium", "type": "uint256"}, {"indexed": true, "internalType": "uint16", "name": "referralCode", "type": "uint16"}], "name": "FlashLoan", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "asset", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "totalDebt", "type": "uint256"}], "name": "IsolationModeTotalDebtUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "collateralAsset", "type": "address"}, {"indexed": true, "internalType": "address", "name": "debtAsset", "type": "address"}, {"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "debtToCover", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "liquidatedCollateralAmount", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "liquidator", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "receiveAToken", "type": "bool"}], "name": "LiquidationCall", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "reserve", "type": "address"}, {"indexed": false, "internalType": "address", "name": "user", "type": "address"}, {"indexed": true, "internalType": "address", "name": "onBehalfOf", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": true, "internalType": "uint16", "name": "referralCode", "type": "uint16"}], "name": "<PERSON><PERSON><PERSON>backed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "reserve", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amountMinted", "type": "uint256"}], "name": "MintedToTreasury", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "reserve", "type": "address"}, {"indexed": true, "internalType": "address", "name": "user", "type": "address"}], "name": "RebalanceStableBorrowRate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "reserve", "type": "address"}, {"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": true, "internalType": "address", "name": "repayer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "useATokens", "type": "bool"}], "name": "<PERSON>ay", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "reserve", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "liquidityRate", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "stableBorrowRate", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "variableBorrowRate", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "liquidityIndex", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "variableBorrowIndex", "type": "uint256"}], "name": "ReserveDataUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "reserve", "type": "address"}, {"indexed": true, "internalType": "address", "name": "user", "type": "address"}], "name": "ReserveUsedAsCollateralDisabled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "reserve", "type": "address"}, {"indexed": true, "internalType": "address", "name": "user", "type": "address"}], "name": "ReserveUsedAsCollateralEnabled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "reserve", "type": "address"}, {"indexed": false, "internalType": "address", "name": "user", "type": "address"}, {"indexed": true, "internalType": "address", "name": "onBehalfOf", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": true, "internalType": "uint16", "name": "referralCode", "type": "uint16"}], "name": "Supply", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "reserve", "type": "address"}, {"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "enum DataTypes.InterestRateMode", "name": "interestRateMode", "type": "uint8"}], "name": "SwapBorrowRateMode", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint8", "name": "categoryId", "type": "uint8"}], "name": "UserEModeSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "reserve", "type": "address"}, {"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Withdraw", "type": "event"}, {"inputs": [], "name": "ADDRESSES_PROVIDER", "outputs": [{"internalType": "contract IPoolAddressesProvider", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "BRIDGE_PROTOCOL_FEE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "FLASHLOAN_PREMIUM_TOTAL", "outputs": [{"internalType": "uint128", "name": "", "type": "uint128"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "FLASHLOAN_PREMIUM_TO_PROTOCOL", "outputs": [{"internalType": "uint128", "name": "", "type": "uint128"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_NUMBER_RESERVES", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_STABLE_RATE_BORROW_SIZE_PERCENT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "POOL_REVISION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "fee", "type": "uint256"}], "name": "backUnbacked", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "interestRateMode", "type": "uint256"}, {"internalType": "uint16", "name": "referralCode", "type": "uint16"}, {"internalType": "address", "name": "onBehalfOf", "type": "address"}], "name": "borrow", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "args", "type": "bytes32"}], "name": "borrow", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint8", "name": "id", "type": "uint8"}, {"components": [{"internalType": "uint16", "name": "ltv", "type": "uint16"}, {"internalType": "uint16", "name": "liquidationThreshold", "type": "uint16"}, {"internalType": "uint16", "name": "liquidationBonus", "type": "uint16"}, {"internalType": "address", "name": "priceSource", "type": "address"}, {"internalType": "string", "name": "label", "type": "string"}], "internalType": "struct DataTypes.EModeCategory", "name": "category", "type": "tuple"}], "name": "configureEModeCategory", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address", "name": "onBehalfOf", "type": "address"}, {"internalType": "uint16", "name": "referralCode", "type": "uint16"}], "name": "deposit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}], "name": "dropReserve", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "balanceFromBefore", "type": "uint256"}, {"internalType": "uint256", "name": "balanceToBefore", "type": "uint256"}], "name": "finalizeTransfer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "receiver<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "address[]", "name": "assets", "type": "address[]"}, {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "interestRateModes", "type": "uint256[]"}, {"internalType": "address", "name": "onBehalfOf", "type": "address"}, {"internalType": "bytes", "name": "params", "type": "bytes"}, {"internalType": "uint16", "name": "referralCode", "type": "uint16"}], "name": "flashLoan", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "receiver<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes", "name": "params", "type": "bytes"}, {"internalType": "uint16", "name": "referralCode", "type": "uint16"}], "name": "flash<PERSON>oan<PERSON><PERSON>ple", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}], "name": "getConfiguration", "outputs": [{"components": [{"internalType": "uint256", "name": "data", "type": "uint256"}], "internalType": "struct DataTypes.ReserveConfigurationMap", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint8", "name": "id", "type": "uint8"}], "name": "getEModeCategoryData", "outputs": [{"components": [{"internalType": "uint16", "name": "ltv", "type": "uint16"}, {"internalType": "uint16", "name": "liquidationThreshold", "type": "uint16"}, {"internalType": "uint16", "name": "liquidationBonus", "type": "uint16"}, {"internalType": "address", "name": "priceSource", "type": "address"}, {"internalType": "string", "name": "label", "type": "string"}], "internalType": "struct DataTypes.EModeCategory", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint16", "name": "id", "type": "uint16"}], "name": "getReserveAddressById", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}], "name": "getReserveData", "outputs": [{"components": [{"components": [{"internalType": "uint256", "name": "data", "type": "uint256"}], "internalType": "struct DataTypes.ReserveConfigurationMap", "name": "configuration", "type": "tuple"}, {"internalType": "uint128", "name": "liquidityIndex", "type": "uint128"}, {"internalType": "uint128", "name": "currentLiquidityRate", "type": "uint128"}, {"internalType": "uint128", "name": "variableBorrowIndex", "type": "uint128"}, {"internalType": "uint128", "name": "currentVariableBorrowRate", "type": "uint128"}, {"internalType": "uint128", "name": "currentStableBorrowRate", "type": "uint128"}, {"internalType": "uint40", "name": "lastUpdateTimestamp", "type": "uint40"}, {"internalType": "uint16", "name": "id", "type": "uint16"}, {"internalType": "address", "name": "aTokenAddress", "type": "address"}, {"internalType": "address", "name": "stableDebtTokenAddress", "type": "address"}, {"internalType": "address", "name": "variableDebtTokenAddress", "type": "address"}, {"internalType": "address", "name": "interestRateStrategyAddress", "type": "address"}, {"internalType": "uint128", "name": "accruedToTreasury", "type": "uint128"}, {"internalType": "uint128", "name": "unbacked", "type": "uint128"}, {"internalType": "uint128", "name": "isolationModeTotalDebt", "type": "uint128"}], "internalType": "struct DataTypes.ReserveData", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}], "name": "getReserveNormalizedIncome", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}], "name": "getReserveNormalizedVariableDebt", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getReservesList", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserAccountData", "outputs": [{"internalType": "uint256", "name": "totalCollateralBase", "type": "uint256"}, {"internalType": "uint256", "name": "totalDebtBase", "type": "uint256"}, {"internalType": "uint256", "name": "availableBorrowsBase", "type": "uint256"}, {"internalType": "uint256", "name": "currentLiquidationThreshold", "type": "uint256"}, {"internalType": "uint256", "name": "ltv", "type": "uint256"}, {"internalType": "uint256", "name": "healthFactor", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserConfiguration", "outputs": [{"components": [{"internalType": "uint256", "name": "data", "type": "uint256"}], "internalType": "struct DataTypes.UserConfigurationMap", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserEMode", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "address", "name": "aTokenAddress", "type": "address"}, {"internalType": "address", "name": "stableDebtAddress", "type": "address"}, {"internalType": "address", "name": "variableDebtAddress", "type": "address"}, {"internalType": "address", "name": "interestRateStrategyAddress", "type": "address"}], "name": "initReserve", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "contract IPoolAddressesProvider", "name": "provider", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "collateralAsset", "type": "address"}, {"internalType": "address", "name": "debtAsset", "type": "address"}, {"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "debtToCover", "type": "uint256"}, {"internalType": "bool", "name": "receiveAToken", "type": "bool"}], "name": "liquidationCall", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "args1", "type": "bytes32"}, {"internalType": "bytes32", "name": "args2", "type": "bytes32"}], "name": "liquidationCall", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "assets", "type": "address[]"}], "name": "mintToTreasury", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address", "name": "onBehalfOf", "type": "address"}, {"internalType": "uint16", "name": "referralCode", "type": "uint16"}], "name": "mintUnbacked", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "args", "type": "bytes32"}], "name": "rebalanceStableBorrowRate", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "address", "name": "user", "type": "address"}], "name": "rebalanceStableBorrowRate", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "args", "type": "bytes32"}], "name": "repay", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "interestRateMode", "type": "uint256"}, {"internalType": "address", "name": "onBehalfOf", "type": "address"}], "name": "repay", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "interestRateMode", "type": "uint256"}], "name": "repayWithATokens", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "args", "type": "bytes32"}], "name": "repayWithATokens", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "args", "type": "bytes32"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "repayWithPermit", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "interestRateMode", "type": "uint256"}, {"internalType": "address", "name": "onBehalfOf", "type": "address"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint8", "name": "permitV", "type": "uint8"}, {"internalType": "bytes32", "name": "permitR", "type": "bytes32"}, {"internalType": "bytes32", "name": "permitS", "type": "bytes32"}], "name": "repayWithPermit", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "rescueTokens", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}], "name": "resetIsolationModeTotalDebt", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"components": [{"internalType": "uint256", "name": "data", "type": "uint256"}], "internalType": "struct DataTypes.ReserveConfigurationMap", "name": "configuration", "type": "tuple"}], "name": "setConfiguration", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "address", "name": "rateStrategyAddress", "type": "address"}], "name": "setReserveInterestRateStrategyAddress", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint8", "name": "categoryId", "type": "uint8"}], "name": "setUserEMode", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "args", "type": "bytes32"}], "name": "setUserUseReserveAsCollateral", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "bool", "name": "useAsCollateral", "type": "bool"}], "name": "setUserUseReserveAsCollateral", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address", "name": "onBehalfOf", "type": "address"}, {"internalType": "uint16", "name": "referralCode", "type": "uint16"}], "name": "supply", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "args", "type": "bytes32"}], "name": "supply", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address", "name": "onBehalfOf", "type": "address"}, {"internalType": "uint16", "name": "referralCode", "type": "uint16"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint8", "name": "permitV", "type": "uint8"}, {"internalType": "bytes32", "name": "permitR", "type": "bytes32"}, {"internalType": "bytes32", "name": "permitS", "type": "bytes32"}], "name": "supplyWithPermit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "args", "type": "bytes32"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "supplyWithPermit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "args", "type": "bytes32"}], "name": "swapBorrowRateMode", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "interestRateMode", "type": "uint256"}], "name": "swapBorrowRateMode", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "protocolFee", "type": "uint256"}], "name": "updateBridgeProtocolFee", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint128", "name": "flashLoanPremiumTotal", "type": "uint128"}, {"internalType": "uint128", "name": "flashLoanPremiumToProtocol", "type": "uint128"}], "name": "updateFlashloanPremiums", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address", "name": "to", "type": "address"}], "name": "withdraw", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "args", "type": "bytes32"}], "name": "withdraw", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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__$f598c634f2d943205ac23f707b80075cbb$__6383c1087d6034603660356037604051806101200160405280603b60089054906101000a900461ffff1661ffff1681526020018981526020018c73ffffffffffffffffffffffffffffffffffffffff1681526020018b73ffffffffffffffffffffffffffffffffffffffff1681526020018a73ffffffffffffffffffffffffffffffffffffffff16815260200188151581526020017f000000000000000000000000000000000000000000000000000000000000000073ffffffffffffffffffffffffffffffffffffffff1663fca513a86040518163ffffffff1660e01b8152600401602060405180830381865afa158015610c01573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610c259190615111565b73ffffffffffffffffffffffffffffffffffffffff90811682528b81166000908152603860209081526040918290205460ff168185015281517f5eb88d3d000000000000000000000000000000000000000000000000000000008152825192909401937f000000000000000000000000000000000000000000000000000000000000000090931692635eb88d3d92600480830193928290030181865afa158015610cd3573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610cf79190615111565b73ffffffffffffffffffffffffffffffffffffffff168152506040518663ffffffff1660e01b8152600401610d3095949392919061512e565b60006040518083038186803b158015610d4857600080fd5b505af4158015610d5c573d6000803e3d6000fd5b505050505050505050565b6040517fd505accf000000000000000000000000000000000000000000000000000000008152336004820152306024820152604481018890526064810185905260ff8416608482015260a4810183905260c4810182905273ffffffffffffffffffffffffffffffffffffffff89169063d505accf9060e401600060405180830381600087803b158015610df957600080fd5b505af1158015610e0d573d6000803e3d6000fd5b5050505073ffffffffffffffffffffffffffffffffffffffff86811660008181526035602090815260409182902082516080810184528d861681529182018c815282840194855261ffff8b81166060850190815294517f1913f16100000000000000000000000000000000000000000000000000000000815260346004820152603660248201526044810193909352925186166064830152516084820152925190931660a48301525190911660c482015273__$db79717e66442ee197e8271d032a066e34$__90631913f1619060e40160006040518083038186803b158015610ef557600080fd5b505af4158015610f09573d6000803e3d6000fd5b505050505050505050505050565b610f1f6139ac565b60408051808201909152600281527f3737000000000000000000000000000000000000000000000000000000000000602082015273ffffffffffffffffffffffffffffffffffffffff8316610faa576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610fa1919061520f565b60405180910390fd5b5073ffffffffffffffffffffffffffffffffffffffff82166000908152603460205260409020600301547501000000000000000000000000000000000000000000900461ffff1615158061104057506000805260366020527f4cb2b152c1b54ce671907a93c300fd5aa72383a9d4ec19a81e3333632ae92e005473ffffffffffffffffffffffffffffffffffffffff8381169116145b6040518060400160405280600281526020017f3832000000000000000000000000000000000000000000000000000000000000815250906110ae576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610fa1919061520f565b5073ffffffffffffffffffffffffffffffffffffffff918216600090815260346020526040902060070180547fffffffffffffffffffffffff00000000000000000000000000000000000000001691909216179055565b600080611113603684613ada565b91509150611121828261216e565b505050565b73__$e4b9550ff526a295e1233dea02821b9004$__635d5dc3136034603660376038603560003373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020016000206040518060600160405280603b60089054906101000a900461ffff1661ffff1681526020017f000000000000000000000000000000000000000000000000000000000000000073ffffffffffffffffffffffffffffffffffffffff1663fca513a86040518163ffffffff1660e01b8152600401602060405180830381865afa158015611217573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061123b9190615111565b73ffffffffffffffffffffffffffffffffffffffff1681526020018960ff168152506040518763ffffffff1660e01b81526004016112d29695949392919095865260208087019590955260408087019490945260608601929092526080850152805160a08501529182015173ffffffffffffffffffffffffffffffffffffffff1660c0840152015160ff1660e08201526101000190565b60006040518083038186803b1580156112ea57600080fd5b505af41580156112fe573d6000803e3d6000fd5b5050505050565b600073__$c3724b8d563dc83a94e797176cddecb3b9$__6340e95de660346036603560003373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020016000206040518060a001604052808a73ffffffffffffffffffffffffffffffffffffffff1681526020018981526020018860028111156113a3576113a3615222565b60028111156113b4576113b4615222565b81523360208201526001604091820152517fffffffff0000000000000000000000000000000000000000000000000000000060e087901b1681526113fe949392919060040161528c565b602060405180830381865af415801561141b573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061143f91906152ff565b90505b9392505050565b6114516139ac565b603955565b73ffffffffffffffffffffffffffffffffffffffff8116600090815260346020526040812061148490613b14565b92915050565b61ffff811660009081526036602052604090205473ffffffffffffffffffffffffffffffffffffffff90811690601083901c166111218282612d4e565b60006040518060e001604052808873ffffffffffffffffffffffffffffffffffffffff1681526020018773ffffffffffffffffffffffffffffffffffffffff16815260200186815260200185858080601f016020809104026020016040519081016040528093929190818152602001838380828437600092018290525093855250505061ffff8516602080840191909152603a546fffffffffffffffffffffffffffffffff70010000000000000000000000000000000082048116604080870191909152911660609094019390935273ffffffffffffffffffffffffffffffffffffffff8a1682526034905281902090517fa1fe0e8d00000000000000000000000000000000000000000000000000000000815291925073__$d5ddd09ae98762b8929dd85e54b218e259$__9163a1fe0e8d91611608918590600401615318565b60006040518083038186803b15801561162057600080fd5b505af4158015611634573d6000803e3d6000fd5b5050505050505050505050565b61ffff811660009081526036602052604090205473ffffffffffffffffffffffffffffffffffffffff16601082901c60011661112182826117f9565b60008060008061168e603686613ba4565b9250925092506116a0838383336116a9565b95945050505050565b600073__$c3724b8d563dc83a94e797176cddecb3b9$__6340e95de660346036603560008773ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020016000206040518060a001604052808b73ffffffffffffffffffffffffffffffffffffffff1681526020018a815260200189600281111561174757611747615222565b600281111561175857611758615222565b815273ffffffffffffffffffffffffffffffffffffffff891660208201526000604091820152517fffffffff0000000000000000000000000000000000000000000000000000000060e087901b1681526117b8949392919060040161528c565b602060405180830381865af41580156117d5573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906116a091906152ff565b73__$db79717e66442ee197e8271d032a066e34$__63bf697a26603460366037603560003373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020016000208787603b60089054906101000a900461ffff167f000000000000000000000000000000000000000000000000000000000000000073ffffffffffffffffffffffffffffffffffffffff1663fca513a86040518163ffffffff1660e01b8152600401602060405180830381865afa1580156118d6573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906118fa9190615111565b336000908152603860205260409081902054905160e08b901b7fffffffff00000000000000000000000000000000000000000000000000000000168152600481019990995260248901979097526044880195909552606487019390935273ffffffffffffffffffffffffffffffffffffffff9182166084870152151560a486015261ffff90911660c48501521660e483015260ff16610104820152610124015b60006040518083038186803b1580156119b257600080fd5b505af41580156119c6573d6000803e3d6000fd5b505050505050565b73ffffffffffffffffffffffffffffffffffffffff8281166000818152603560209081526040918290208251608081018452898616815291820188815282840194855261ffff8781166060850190815294517f1913f16100000000000000000000000000000000000000000000000000000000815260346004820152603660248201526044810193909352925186166064830152516084820152925190931660a48301525190911660c482015273__$db79717e66442ee197e8271d032a066e34$__90631913f1619060e4015b60006040518083038186803b158015611ab357600080fd5b505af4158015611ac7573d6000803e3d6000fd5b5050505050505050565b611ad96139ac565b6040517f9cf57023000000000000000000000000000000000000000000000000000000008152603460048201526036602482015273ffffffffffffffffffffffffffffffffffffffff8216604482015273__$563c746fa3df0f1858d85f6ef4258864be$__90639cf57023906064016112d2565b6000806000806000611b60603689613c32565b94509450945094509450611ac78585338686868d8d610d67565b600073__$db79717e66442ee197e8271d032a066e34$__63186dea44603460366037603560003373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020016000206040518060c001604052808b73ffffffffffffffffffffffffffffffffffffffff1681526020018a81526020018973ffffffffffffffffffffffffffffffffffffffff168152602001603b60089054906101000a900461ffff1661ffff1681526020017f000000000000000000000000000000000000000000000000000000000000000073ffffffffffffffffffffffffffffffffffffffff1663fca513a86040518163ffffffff1660e01b8152600401602060405180830381865afa158015611ca9573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190611ccd9190615111565b73ffffffffffffffffffffffffffffffffffffffff9081168252336000908152603860209081526040918290205460ff90811694820194909452815160e08b901b7fffffffff0000000000000000000000000000000000000000000000000000000016815260048101999099526024890197909752604488019590955260648701939093528151831660848701529381015160a486015291820151811660c4850152606082015160e485015260808201511661010484015260a0015116610124820152610144016113fe565b611da1613cbc565b73ffffffffffffffffffffffffffffffffffffffff8281166000818152603560205260409081902090517f0413c86f0000000000000000000000000000000000000000000000000000000081526034600482015260366024820152604481019190915291861660648301526084820185905260a482015261ffff821660c482015273__$b06080f092f400a43662c3f835a4d9baa8$__90630413c86f9060e401611a9b565b6040805160a081018252600080825260208201819052918101829052606080820192909252608081019190915260ff8216600090815260376020908152604091829020825160a081018452815461ffff8082168352620100008204811694830194909452640100000000810490931693810193909352660100000000000090910473ffffffffffffffffffffffffffffffffffffffff166060830152600181018054608084019190611ef7906153a3565b80601f0160208091040260200160405190810160405280929190818152602001828054611f23906153a3565b8015611f705780601f10611f4557610100808354040283529160200191611f70565b820191906000526020600020905b815481529060010190602001808311611f5357829003601f168201915b5050505050815250509050919050565b611f886139ac565b73__$563c746fa3df0f1858d85f6ef4258864be$__6369fc1bdf603460366040518060e001604052808a73ffffffffffffffffffffffffffffffffffffffff1681526020018973ffffffffffffffffffffffffffffffffffffffff1681526020018873ffffffffffffffffffffffffffffffffffffffff1681526020018773ffffffffffffffffffffffffffffffffffffffff1681526020018673ffffffffffffffffffffffffffffffffffffffff168152602001603b60089054906101000a900461ffff1661ffff16815260200161205f608090565b61ffff168152506040518463ffffffff1660e01b8152600401612084939291906153f1565b602060405180830381865af41580156120a1573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906120c59190615481565b156112fe57603b805468010000000000000000900461ffff169060086120ea836154cd565b91906101000a81548161ffff021916908361ffff160217905550505050505050565b600080600061211c603685613e49565b9150915061212b828233611b7a565b949350505050565b60008060008060008061214760368a613ec9565b945094509450945094506121618585853386868e8e61358f565b9998505050505050505050565b73ffffffffffffffffffffffffffffffffffffffff82166000908152603460209081526040808320338452603590925290912073__$c3724b8d563dc83a94e797176cddecb3b9$__9163eac4d70391858560028111156121d0576121d0615222565b6040518563ffffffff1660e01b815260040161199a94939291906154ef565b6040517f48c2ca8c00000000000000000000000000000000000000000000000000000000815273__$563c746fa3df0f1858d85f6ef4258864be$__906348c2ca8c9061199a9060349086908690600401615526565b73__$c3724b8d563dc83a94e797176cddecb3b9$__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__$d5ddd09ae98762b8929dd85e54b218e259$__91632e7263ea916128a591603491603691603791908890600401615734565b60006040518083038186803b1580156128bd57600080fd5b505af41580156128d1573d6000803e3d6000fd5b50505050505050505050505050505050565b6128eb6139ac565b6fffffffffffffffffffffffffffffffff90811670010000000000000000000000000000000002911617603a55565b6040805173ffffffffffffffffffffffffffffffffffffffff83811660008181526035602090815285822060c0860187525460a086019081528552603b5468010000000000000000900461ffff16818601528486019290925284517ffca513a8000000000000000000000000000000000000000000000000000000008152945190948594859485948594859473__$563c746fa3df0f1858d85f6ef4258864be$__946326ec273f9460349460369460379460608501937f0000000000000000000000000000000000000000000000000000000000000000169263fca513a8926004808401938290030181865afa158015612a18573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190612a3c9190615111565b73ffffffffffffffffffffffffffffffffffffffff90811682528e81166000908152603860209081526040918290205460ff90811694820194909452815160e08a901b7fffffffff00000000000000000000000000000000000000000000000000000000168152600481019890985260248801969096526044870194909452825151606487015293820151608486015291810151831660a4850152606081015190921660c48401526080909101511660e48201526101040160c060405180830381865af4158015612b11573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190612b3591906158da565b949c939b5091995097509550909350915050565b60015460039060ff1680612b5c5750303b155b80612b68575060005481115b612bf4576040517f08c379a000000000000000000000000000000000000000000000000000000000815260206004820152602e60248201527f436f6e747261637420696e7374616e63652068617320616c726561647920626560448201527f656e20696e697469616c697a65640000000000000000000000000000000000006064820152608401610fa1565b60015460ff16158015612c3157600180547fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff00168117905560008290555b7f000000000000000000000000000000000000000000000000000000000000000073ffffffffffffffffffffffffffffffffffffffff168373ffffffffffffffffffffffffffffffffffffffff16146040518060400160405280600281526020017f313200000000000000000000000000000000000000000000000000000000000081525090612cee576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610fa1919061520f565b50603b80547fffffffffffffffffffffffffffffffffffffffffffffffff0000000000000000166109c4179055801561112157600180547fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff00169055505050565b73ffffffffffffffffffffffffffffffffffffffff8281166000818152603460205260409081902090517f6973f74400000000000000000000000000000000000000000000000000000000815260048101919091526024810191909152908216604482015273__$c3724b8d563dc83a94e797176cddecb3b9$__90636973f7449060640161199a565b612ddf613f09565b6040517f87b322b200000000000000000000000000000000000000000000000000000000815273ffffffffffffffffffffffffffffffffffffffff8085166004830152831660248201526044810182905273__$563c746fa3df0f1858d85f6ef4258864be$__906387b322b29060640160006040518083038186803b158015612e6757600080fd5b505af4158015612e7b573d6000803e3d6000fd5b50505050505050565b73ffffffffffffffffffffffffffffffffffffffff8116600090815260346020526040812061148490614096565b603b5460609068010000000000000000900461ffff166000808267ffffffffffffffff811115612ee457612ee4614e06565b604051908082528060200260200182016040528015612f0d578160200160208202803683370190505b50905060005b83811015612fe45760008181526036602052604090205473ffffffffffffffffffffffffffffffffffffffff1615612fc45760008181526036602052604090205473ffffffffffffffffffffffffffffffffffffffff1682612f758584615924565b81518110612f8557612f8561593b565b602002602001019073ffffffffffffffffffffffffffffffffffffffff16908173ffffffffffffffffffffffffffffffffffffffff1681525050612fd2565b82612fce8161596a565b9350505b80612fdc8161596a565b915050612f13565b5091038152919050565b612ff66139ac565b60408051808201909152600281527f3136000000000000000000000000000000000000000000000000000000000000602082015260ff8316613065576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610fa1919061520f565b5060ff8216600090815260376020908152604091829020835181548386015194860151606087015173ffffffffffffffffffffffffffffffffffffffff166601000000000000027fffffffffffff0000000000000000000000000000000000000000ffffffffffff61ffff92831664010000000002167fffffffffffff00000000000000000000000000000000000000000000ffffffff97831662010000027fffffffffffffffffffffffffffffffffffffffffffffffffffffffff000000009094169290941691909117919091179490941617929092178255608083015180518493926112fe9260018501929101906143c6565b73ffffffffffffffffffffffffffffffffffffffff868116600090815260346020908152604091829020600401548251808401909352600283527f31310000000000000000000000000000000000000000000000000000000000009183019190915290911633146131f8576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610fa1919061520f565b5073__$db79717e66442ee197e8271d032a066e34$__638a5dadd160346036603760356040518061012001604052808d73ffffffffffffffffffffffffffffffffffffffff1681526020018c73ffffffffffffffffffffffffffffffffffffffff1681526020018b73ffffffffffffffffffffffffffffffffffffffff1681526020018a8152602001898152602001888152602001603b60089054906101000a900461ffff1661ffff1681526020017f000000000000000000000000000000000000000000000000000000000000000073ffffffffffffffffffffffffffffffffffffffff1663fca513a86040518163ffffffff1660e01b8152600401602060405180830381865afa158015613312573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906133369190615111565b73ffffffffffffffffffffffffffffffffffffffff90811682528d166000908152603860209081526040918290205460ff16920191909152517fffffffff0000000000000000000000000000000000000000000000000000000060e088901b1681526133a99594939291906004016159a3565b60006040518083038186803b1580156133c157600080fd5b505af41580156133d5573d6000803e3d6000fd5b50505050505050505050565b60008060008061344360368661ffff818116600090815260209390935260409092205473ffffffffffffffffffffffffffffffffffffffff16926fffffffffffffffffffffffffffffffff601083901c169260ff609084901c169260981c1690565b93509350935093506112fe8484848433612244565b6000613462613cbc565b73ffffffffffffffffffffffffffffffffffffffff84166000818152603460205260409081902060395491517f8e743248000000000000000000000000000000000000000000000000000000008152600481019190915260248101929092526044820185905260648201849052608482015273__$b06080f092f400a43662c3f835a4d9baa8$__90638e7432489060a4016113fe565b600080600080613509603686613ba4565b9250925092506116a0838383611305565b6135226139ac565b6040517f1e3b41450000000000000000000000000000000000000000000000000000000081526034600482015273ffffffffffffffffffffffffffffffffffffffff8216602482015273__$563c746fa3df0f1858d85f6ef4258864be$__90631e3b4145906044016112d2565b6040517fd505accf000000000000000000000000000000000000000000000000000000008152336004820152306024820152604481018890526064810185905260ff8416608482015260a4810183905260c4810182905260009073ffffffffffffffffffffffffffffffffffffffff8a169063d505accf9060e401600060405180830381600087803b15801561362457600080fd5b505af1158015613638573d6000803e3d6000fd5b5050505060006040518060a001604052808b73ffffffffffffffffffffffffffffffffffffffff1681526020018a815260200189600281111561367d5761367d615222565b600281111561368e5761368e615222565b815273ffffffffffffffffffffffffffffffffffffffff89166020808301829052600060409384018190529182526035905281902090517f40e95de600000000000000000000000000000000000000000000000000000000815291925073__$c3724b8d563dc83a94e797176cddecb3b9$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", "deployedBytecode": "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__$f598c634f2d943205ac23f707b80075cbb$__6383c1087d6034603660356037604051806101200160405280603b60089054906101000a900461ffff1661ffff1681526020018981526020018c73ffffffffffffffffffffffffffffffffffffffff1681526020018b73ffffffffffffffffffffffffffffffffffffffff1681526020018a73ffffffffffffffffffffffffffffffffffffffff16815260200188151581526020017f000000000000000000000000000000000000000000000000000000000000000073ffffffffffffffffffffffffffffffffffffffff1663fca513a86040518163ffffffff1660e01b8152600401602060405180830381865afa158015610c01573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610c259190615111565b73ffffffffffffffffffffffffffffffffffffffff90811682528b81166000908152603860209081526040918290205460ff168185015281517f5eb88d3d000000000000000000000000000000000000000000000000000000008152825192909401937f000000000000000000000000000000000000000000000000000000000000000090931692635eb88d3d92600480830193928290030181865afa158015610cd3573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610cf79190615111565b73ffffffffffffffffffffffffffffffffffffffff168152506040518663ffffffff1660e01b8152600401610d3095949392919061512e565b60006040518083038186803b158015610d4857600080fd5b505af4158015610d5c573d6000803e3d6000fd5b505050505050505050565b6040517fd505accf000000000000000000000000000000000000000000000000000000008152336004820152306024820152604481018890526064810185905260ff8416608482015260a4810183905260c4810182905273ffffffffffffffffffffffffffffffffffffffff89169063d505accf9060e401600060405180830381600087803b158015610df957600080fd5b505af1158015610e0d573d6000803e3d6000fd5b5050505073ffffffffffffffffffffffffffffffffffffffff86811660008181526035602090815260409182902082516080810184528d861681529182018c815282840194855261ffff8b81166060850190815294517f1913f16100000000000000000000000000000000000000000000000000000000815260346004820152603660248201526044810193909352925186166064830152516084820152925190931660a48301525190911660c482015273__$db79717e66442ee197e8271d032a066e34$__90631913f1619060e40160006040518083038186803b158015610ef557600080fd5b505af4158015610f09573d6000803e3d6000fd5b505050505050505050505050565b610f1f6139ac565b60408051808201909152600281527f3737000000000000000000000000000000000000000000000000000000000000602082015273ffffffffffffffffffffffffffffffffffffffff8316610faa576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610fa1919061520f565b60405180910390fd5b5073ffffffffffffffffffffffffffffffffffffffff82166000908152603460205260409020600301547501000000000000000000000000000000000000000000900461ffff1615158061104057506000805260366020527f4cb2b152c1b54ce671907a93c300fd5aa72383a9d4ec19a81e3333632ae92e005473ffffffffffffffffffffffffffffffffffffffff8381169116145b6040518060400160405280600281526020017f3832000000000000000000000000000000000000000000000000000000000000815250906110ae576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610fa1919061520f565b5073ffffffffffffffffffffffffffffffffffffffff918216600090815260346020526040902060070180547fffffffffffffffffffffffff00000000000000000000000000000000000000001691909216179055565b600080611113603684613ada565b91509150611121828261216e565b505050565b73__$e4b9550ff526a295e1233dea02821b9004$__635d5dc3136034603660376038603560003373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020016000206040518060600160405280603b60089054906101000a900461ffff1661ffff1681526020017f000000000000000000000000000000000000000000000000000000000000000073ffffffffffffffffffffffffffffffffffffffff1663fca513a86040518163ffffffff1660e01b8152600401602060405180830381865afa158015611217573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061123b9190615111565b73ffffffffffffffffffffffffffffffffffffffff1681526020018960ff168152506040518763ffffffff1660e01b81526004016112d29695949392919095865260208087019590955260408087019490945260608601929092526080850152805160a08501529182015173ffffffffffffffffffffffffffffffffffffffff1660c0840152015160ff1660e08201526101000190565b60006040518083038186803b1580156112ea57600080fd5b505af41580156112fe573d6000803e3d6000fd5b5050505050565b600073__$c3724b8d563dc83a94e797176cddecb3b9$__6340e95de660346036603560003373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020016000206040518060a001604052808a73ffffffffffffffffffffffffffffffffffffffff1681526020018981526020018860028111156113a3576113a3615222565b60028111156113b4576113b4615222565b81523360208201526001604091820152517fffffffff0000000000000000000000000000000000000000000000000000000060e087901b1681526113fe949392919060040161528c565b602060405180830381865af415801561141b573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061143f91906152ff565b90505b9392505050565b6114516139ac565b603955565b73ffffffffffffffffffffffffffffffffffffffff8116600090815260346020526040812061148490613b14565b92915050565b61ffff811660009081526036602052604090205473ffffffffffffffffffffffffffffffffffffffff90811690601083901c166111218282612d4e565b60006040518060e001604052808873ffffffffffffffffffffffffffffffffffffffff1681526020018773ffffffffffffffffffffffffffffffffffffffff16815260200186815260200185858080601f016020809104026020016040519081016040528093929190818152602001838380828437600092018290525093855250505061ffff8516602080840191909152603a546fffffffffffffffffffffffffffffffff70010000000000000000000000000000000082048116604080870191909152911660609094019390935273ffffffffffffffffffffffffffffffffffffffff8a1682526034905281902090517fa1fe0e8d00000000000000000000000000000000000000000000000000000000815291925073__$d5ddd09ae98762b8929dd85e54b218e259$__9163a1fe0e8d91611608918590600401615318565b60006040518083038186803b15801561162057600080fd5b505af4158015611634573d6000803e3d6000fd5b5050505050505050505050565b61ffff811660009081526036602052604090205473ffffffffffffffffffffffffffffffffffffffff16601082901c60011661112182826117f9565b60008060008061168e603686613ba4565b9250925092506116a0838383336116a9565b95945050505050565b600073__$c3724b8d563dc83a94e797176cddecb3b9$__6340e95de660346036603560008773ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020016000206040518060a001604052808b73ffffffffffffffffffffffffffffffffffffffff1681526020018a815260200189600281111561174757611747615222565b600281111561175857611758615222565b815273ffffffffffffffffffffffffffffffffffffffff891660208201526000604091820152517fffffffff0000000000000000000000000000000000000000000000000000000060e087901b1681526117b8949392919060040161528c565b602060405180830381865af41580156117d5573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906116a091906152ff565b73__$db79717e66442ee197e8271d032a066e34$__63bf697a26603460366037603560003373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020016000208787603b60089054906101000a900461ffff167f000000000000000000000000000000000000000000000000000000000000000073ffffffffffffffffffffffffffffffffffffffff1663fca513a86040518163ffffffff1660e01b8152600401602060405180830381865afa1580156118d6573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906118fa9190615111565b336000908152603860205260409081902054905160e08b901b7fffffffff00000000000000000000000000000000000000000000000000000000168152600481019990995260248901979097526044880195909552606487019390935273ffffffffffffffffffffffffffffffffffffffff9182166084870152151560a486015261ffff90911660c48501521660e483015260ff16610104820152610124015b60006040518083038186803b1580156119b257600080fd5b505af41580156119c6573d6000803e3d6000fd5b505050505050565b73ffffffffffffffffffffffffffffffffffffffff8281166000818152603560209081526040918290208251608081018452898616815291820188815282840194855261ffff8781166060850190815294517f1913f16100000000000000000000000000000000000000000000000000000000815260346004820152603660248201526044810193909352925186166064830152516084820152925190931660a48301525190911660c482015273__$db79717e66442ee197e8271d032a066e34$__90631913f1619060e4015b60006040518083038186803b158015611ab357600080fd5b505af4158015611ac7573d6000803e3d6000fd5b5050505050505050565b611ad96139ac565b6040517f9cf57023000000000000000000000000000000000000000000000000000000008152603460048201526036602482015273ffffffffffffffffffffffffffffffffffffffff8216604482015273__$563c746fa3df0f1858d85f6ef4258864be$__90639cf57023906064016112d2565b6000806000806000611b60603689613c32565b94509450945094509450611ac78585338686868d8d610d67565b600073__$db79717e66442ee197e8271d032a066e34$__63186dea44603460366037603560003373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020016000206040518060c001604052808b73ffffffffffffffffffffffffffffffffffffffff1681526020018a81526020018973ffffffffffffffffffffffffffffffffffffffff168152602001603b60089054906101000a900461ffff1661ffff1681526020017f000000000000000000000000000000000000000000000000000000000000000073ffffffffffffffffffffffffffffffffffffffff1663fca513a86040518163ffffffff1660e01b8152600401602060405180830381865afa158015611ca9573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190611ccd9190615111565b73ffffffffffffffffffffffffffffffffffffffff9081168252336000908152603860209081526040918290205460ff90811694820194909452815160e08b901b7fffffffff0000000000000000000000000000000000000000000000000000000016815260048101999099526024890197909752604488019590955260648701939093528151831660848701529381015160a486015291820151811660c4850152606082015160e485015260808201511661010484015260a0015116610124820152610144016113fe565b611da1613cbc565b73ffffffffffffffffffffffffffffffffffffffff8281166000818152603560205260409081902090517f0413c86f0000000000000000000000000000000000000000000000000000000081526034600482015260366024820152604481019190915291861660648301526084820185905260a482015261ffff821660c482015273__$b06080f092f400a43662c3f835a4d9baa8$__90630413c86f9060e401611a9b565b6040805160a081018252600080825260208201819052918101829052606080820192909252608081019190915260ff8216600090815260376020908152604091829020825160a081018452815461ffff8082168352620100008204811694830194909452640100000000810490931693810193909352660100000000000090910473ffffffffffffffffffffffffffffffffffffffff166060830152600181018054608084019190611ef7906153a3565b80601f0160208091040260200160405190810160405280929190818152602001828054611f23906153a3565b8015611f705780601f10611f4557610100808354040283529160200191611f70565b820191906000526020600020905b815481529060010190602001808311611f5357829003601f168201915b5050505050815250509050919050565b611f886139ac565b73__$563c746fa3df0f1858d85f6ef4258864be$__6369fc1bdf603460366040518060e001604052808a73ffffffffffffffffffffffffffffffffffffffff1681526020018973ffffffffffffffffffffffffffffffffffffffff1681526020018873ffffffffffffffffffffffffffffffffffffffff1681526020018773ffffffffffffffffffffffffffffffffffffffff1681526020018673ffffffffffffffffffffffffffffffffffffffff168152602001603b60089054906101000a900461ffff1661ffff16815260200161205f608090565b61ffff168152506040518463ffffffff1660e01b8152600401612084939291906153f1565b602060405180830381865af41580156120a1573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906120c59190615481565b156112fe57603b805468010000000000000000900461ffff169060086120ea836154cd565b91906101000a81548161ffff021916908361ffff160217905550505050505050565b600080600061211c603685613e49565b9150915061212b828233611b7a565b949350505050565b60008060008060008061214760368a613ec9565b945094509450945094506121618585853386868e8e61358f565b9998505050505050505050565b73ffffffffffffffffffffffffffffffffffffffff82166000908152603460209081526040808320338452603590925290912073__$c3724b8d563dc83a94e797176cddecb3b9$__9163eac4d70391858560028111156121d0576121d0615222565b6040518563ffffffff1660e01b815260040161199a94939291906154ef565b6040517f48c2ca8c00000000000000000000000000000000000000000000000000000000815273__$563c746fa3df0f1858d85f6ef4258864be$__906348c2ca8c9061199a9060349086908690600401615526565b73__$c3724b8d563dc83a94e797176cddecb3b9$__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__$d5ddd09ae98762b8929dd85e54b218e259$__91632e7263ea916128a591603491603691603791908890600401615734565b60006040518083038186803b1580156128bd57600080fd5b505af41580156128d1573d6000803e3d6000fd5b50505050505050505050505050505050565b6128eb6139ac565b6fffffffffffffffffffffffffffffffff90811670010000000000000000000000000000000002911617603a55565b6040805173ffffffffffffffffffffffffffffffffffffffff83811660008181526035602090815285822060c0860187525460a086019081528552603b5468010000000000000000900461ffff16818601528486019290925284517ffca513a8000000000000000000000000000000000000000000000000000000008152945190948594859485948594859473__$563c746fa3df0f1858d85f6ef4258864be$__946326ec273f9460349460369460379460608501937f0000000000000000000000000000000000000000000000000000000000000000169263fca513a8926004808401938290030181865afa158015612a18573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190612a3c9190615111565b73ffffffffffffffffffffffffffffffffffffffff90811682528e81166000908152603860209081526040918290205460ff90811694820194909452815160e08a901b7fffffffff00000000000000000000000000000000000000000000000000000000168152600481019890985260248801969096526044870194909452825151606487015293820151608486015291810151831660a4850152606081015190921660c48401526080909101511660e48201526101040160c060405180830381865af4158015612b11573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190612b3591906158da565b949c939b5091995097509550909350915050565b60015460039060ff1680612b5c5750303b155b80612b68575060005481115b612bf4576040517f08c379a000000000000000000000000000000000000000000000000000000000815260206004820152602e60248201527f436f6e747261637420696e7374616e63652068617320616c726561647920626560448201527f656e20696e697469616c697a65640000000000000000000000000000000000006064820152608401610fa1565b60015460ff16158015612c3157600180547fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff00168117905560008290555b7f000000000000000000000000000000000000000000000000000000000000000073ffffffffffffffffffffffffffffffffffffffff168373ffffffffffffffffffffffffffffffffffffffff16146040518060400160405280600281526020017f313200000000000000000000000000000000000000000000000000000000000081525090612cee576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610fa1919061520f565b50603b80547fffffffffffffffffffffffffffffffffffffffffffffffff0000000000000000166109c4179055801561112157600180547fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff00169055505050565b73ffffffffffffffffffffffffffffffffffffffff8281166000818152603460205260409081902090517f6973f74400000000000000000000000000000000000000000000000000000000815260048101919091526024810191909152908216604482015273__$c3724b8d563dc83a94e797176cddecb3b9$__90636973f7449060640161199a565b612ddf613f09565b6040517f87b322b200000000000000000000000000000000000000000000000000000000815273ffffffffffffffffffffffffffffffffffffffff8085166004830152831660248201526044810182905273__$563c746fa3df0f1858d85f6ef4258864be$__906387b322b29060640160006040518083038186803b158015612e6757600080fd5b505af4158015612e7b573d6000803e3d6000fd5b50505050505050565b73ffffffffffffffffffffffffffffffffffffffff8116600090815260346020526040812061148490614096565b603b5460609068010000000000000000900461ffff166000808267ffffffffffffffff811115612ee457612ee4614e06565b604051908082528060200260200182016040528015612f0d578160200160208202803683370190505b50905060005b83811015612fe45760008181526036602052604090205473ffffffffffffffffffffffffffffffffffffffff1615612fc45760008181526036602052604090205473ffffffffffffffffffffffffffffffffffffffff1682612f758584615924565b81518110612f8557612f8561593b565b602002602001019073ffffffffffffffffffffffffffffffffffffffff16908173ffffffffffffffffffffffffffffffffffffffff1681525050612fd2565b82612fce8161596a565b9350505b80612fdc8161596a565b915050612f13565b5091038152919050565b612ff66139ac565b60408051808201909152600281527f3136000000000000000000000000000000000000000000000000000000000000602082015260ff8316613065576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610fa1919061520f565b5060ff8216600090815260376020908152604091829020835181548386015194860151606087015173ffffffffffffffffffffffffffffffffffffffff166601000000000000027fffffffffffff0000000000000000000000000000000000000000ffffffffffff61ffff92831664010000000002167fffffffffffff00000000000000000000000000000000000000000000ffffffff97831662010000027fffffffffffffffffffffffffffffffffffffffffffffffffffffffff000000009094169290941691909117919091179490941617929092178255608083015180518493926112fe9260018501929101906143c6565b73ffffffffffffffffffffffffffffffffffffffff868116600090815260346020908152604091829020600401548251808401909352600283527f31310000000000000000000000000000000000000000000000000000000000009183019190915290911633146131f8576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610fa1919061520f565b5073__$db79717e66442ee197e8271d032a066e34$__638a5dadd160346036603760356040518061012001604052808d73ffffffffffffffffffffffffffffffffffffffff1681526020018c73ffffffffffffffffffffffffffffffffffffffff1681526020018b73ffffffffffffffffffffffffffffffffffffffff1681526020018a8152602001898152602001888152602001603b60089054906101000a900461ffff1661ffff1681526020017f000000000000000000000000000000000000000000000000000000000000000073ffffffffffffffffffffffffffffffffffffffff1663fca513a86040518163ffffffff1660e01b8152600401602060405180830381865afa158015613312573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906133369190615111565b73ffffffffffffffffffffffffffffffffffffffff90811682528d166000908152603860209081526040918290205460ff16920191909152517fffffffff0000000000000000000000000000000000000000000000000000000060e088901b1681526133a99594939291906004016159a3565b60006040518083038186803b1580156133c157600080fd5b505af41580156133d5573d6000803e3d6000fd5b50505050505050505050565b60008060008061344360368661ffff818116600090815260209390935260409092205473ffffffffffffffffffffffffffffffffffffffff16926fffffffffffffffffffffffffffffffff601083901c169260ff609084901c169260981c1690565b93509350935093506112fe8484848433612244565b6000613462613cbc565b73ffffffffffffffffffffffffffffffffffffffff84166000818152603460205260409081902060395491517f8e743248000000000000000000000000000000000000000000000000000000008152600481019190915260248101929092526044820185905260648201849052608482015273__$b06080f092f400a43662c3f835a4d9baa8$__90638e7432489060a4016113fe565b600080600080613509603686613ba4565b9250925092506116a0838383611305565b6135226139ac565b6040517f1e3b41450000000000000000000000000000000000000000000000000000000081526034600482015273ffffffffffffffffffffffffffffffffffffffff8216602482015273__$563c746fa3df0f1858d85f6ef4258864be$__90631e3b4145906044016112d2565b6040517fd505accf000000000000000000000000000000000000000000000000000000008152336004820152306024820152604481018890526064810185905260ff8416608482015260a4810183905260c4810182905260009073ffffffffffffffffffffffffffffffffffffffff8a169063d505accf9060e401600060405180830381600087803b15801561362457600080fd5b505af1158015613638573d6000803e3d6000fd5b5050505060006040518060a001604052808b73ffffffffffffffffffffffffffffffffffffffff1681526020018a815260200189600281111561367d5761367d615222565b600281111561368e5761368e615222565b815273ffffffffffffffffffffffffffffffffffffffff89166020808301829052600060409384018190529182526035905281902090517f40e95de600000000000000000000000000000000000000000000000000000000815291925073__$c3724b8d563dc83a94e797176cddecb3b9$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", "linkReferences": {"contracts/protocol/libraries/logic/BorrowLogic.sol": {"BorrowLogic": [{"length": 20, "start": 5123}, {"length": 20, "start": 6055}, {"length": 20, "start": 8861}, {"length": 20, "start": 9024}, {"length": 20, "start": 11951}, {"length": 20, "start": 14311}]}, "contracts/protocol/libraries/logic/BridgeLogic.sol": {"BridgeLogic": [{"length": 20, "start": 7966}, {"length": 20, "start": 13776}]}, "contracts/protocol/libraries/logic/EModeLogic.sol": {"EModeLogic": [{"length": 20, "start": 4642}]}, "contracts/protocol/libraries/logic/FlashLoanLogic.sol": {"FlashLoanLogic": [{"length": 20, "start": 5850}, {"length": 20, "start": 10605}]}, "contracts/protocol/libraries/logic/LiquidationLogic.sol": {"LiquidationLogic": [{"length": 20, "start": 3048}]}, "contracts/protocol/libraries/logic/PoolLogic.sol": {"PoolLogic": [{"length": 20, "start": 7205}, {"length": 20, "start": 8324}, {"length": 20, "start": 8977}, {"length": 20, "start": 10914}, {"length": 20, "start": 12076}, {"length": 20, "start": 13927}]}, "contracts/protocol/libraries/logic/SupplyLogic.sol": {"SupplyLogic": [{"length": 20, "start": 4026}, {"length": 20, "start": 6389}, {"length": 20, "start": 7031}, {"length": 20, "start": 7288}, {"length": 20, "start": 13045}]}}, "deployedLinkReferences": {"contracts/protocol/libraries/logic/BorrowLogic.sol": {"BorrowLogic": [{"length": 20, "start": 4873}, {"length": 20, "start": 5805}, {"length": 20, "start": 8611}, {"length": 20, "start": 8774}, {"length": 20, "start": 11701}, {"length": 20, "start": 14061}]}, "contracts/protocol/libraries/logic/BridgeLogic.sol": {"BridgeLogic": [{"length": 20, "start": 7716}, {"length": 20, "start": 13526}]}, "contracts/protocol/libraries/logic/EModeLogic.sol": {"EModeLogic": [{"length": 20, "start": 4392}]}, "contracts/protocol/libraries/logic/FlashLoanLogic.sol": {"FlashLoanLogic": [{"length": 20, "start": 5600}, {"length": 20, "start": 10355}]}, "contracts/protocol/libraries/logic/LiquidationLogic.sol": {"LiquidationLogic": [{"length": 20, "start": 2798}]}, "contracts/protocol/libraries/logic/PoolLogic.sol": {"PoolLogic": [{"length": 20, "start": 6955}, {"length": 20, "start": 8074}, {"length": 20, "start": 8727}, {"length": 20, "start": 10664}, {"length": 20, "start": 11826}, {"length": 20, "start": 13677}]}, "contracts/protocol/libraries/logic/SupplyLogic.sol": {"SupplyLogic": [{"length": 20, "start": 3776}, {"length": 20, "start": 6139}, {"length": 20, "start": 6781}, {"length": 20, "start": 7038}, {"length": 20, "start": 12795}]}}}