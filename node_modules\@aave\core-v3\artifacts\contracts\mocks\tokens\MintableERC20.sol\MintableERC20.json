{"_format": "hh-sol-artifact-1", "contractName": "MintableERC20", "sourceName": "contracts/mocks/tokens/MintableERC20.sol", "abi": [{"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [], "name": "DOMAIN_SEPARATOR", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "EIP712_REVISION", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PERMIT_TYPEHASH", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "subtractedValue", "type": "uint256"}], "name": "decreaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "addedValue", "type": "uint256"}], "name": "increaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "mint", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "mint", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "nonces", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "permit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}