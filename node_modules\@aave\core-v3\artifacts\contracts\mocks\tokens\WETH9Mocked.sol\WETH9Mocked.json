{"_format": "hh-sol-artifact-1", "contractName": "WETH9Mocked", "sourceName": "contracts/mocks/tokens/WETH9Mocked.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "src", "type": "address"}, {"indexed": true, "internalType": "address", "name": "guy", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "wad", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "dst", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "wad", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "src", "type": "address"}, {"indexed": true, "internalType": "address", "name": "dst", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "wad", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "src", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "wad", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON>", "type": "event"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "guy", "type": "address"}, {"internalType": "uint256", "name": "wad", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "deposit", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "mint", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "mint", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "dst", "type": "address"}, {"internalType": "uint256", "name": "wad", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "src", "type": "address"}, {"internalType": "address", "name": "dst", "type": "address"}, {"internalType": "uint256", "name": "wad", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "wad", "type": "uint256"}], "name": "withdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}