{"_format": "hh-sol-artifact-1", "contractName": "BaseUpgradeabilityProxy", "sourceName": "contracts/dependencies/openzeppelin/upgradeability/BaseUpgradeabilityProxy.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "Upgraded", "type": "event"}, {"stateMutability": "payable", "type": "fallback"}], "bytecode": "0x6080604052348015600f57600080fd5b5060948061001e6000396000f3fe6080604052600a600c565b005b603960357f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc5490565b603b565b565b3660008037600080366000845af43d6000803e8080156059573d6000f35b3d6000fdfea26469706673582212206ed40777d784fa472aed58d1150b91e36c194d20a148d7eb729a2bd67dd3f5d464736f6c634300080a0033", "deployedBytecode": "0x6080604052600a600c565b005b603960357f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc5490565b603b565b565b3660008037600080366000845af43d6000803e8080156059573d6000f35b3d6000fdfea26469706673582212206ed40777d784fa472aed58d1150b91e36c194d20a148d7eb729a2bd67dd3f5d464736f6c634300080a0033", "linkReferences": {}, "deployedLinkReferences": {}}