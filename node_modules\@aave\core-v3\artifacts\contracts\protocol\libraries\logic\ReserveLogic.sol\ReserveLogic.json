{"_format": "hh-sol-artifact-1", "contractName": "ReserveLogic", "sourceName": "contracts/protocol/libraries/logic/ReserveLogic.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "reserve", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "liquidityRate", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "stableBorrowRate", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "variableBorrowRate", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "liquidityIndex", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "variableBorrowIndex", "type": "uint256"}], "name": "ReserveDataUpdated", "type": "event"}], "bytecode": "0x60566037600b82828239805160001a607314602a57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220b67b48e38d0b6703d367ce4558f5085a19bced7f68aeb09d1e6a84ae221ac48e64736f6c634300080a0033", "deployedBytecode": "0x73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220b67b48e38d0b6703d367ce4558f5085a19bced7f68aeb09d1e6a84ae221ac48e64736f6c634300080a0033", "linkReferences": {}, "deployedLinkReferences": {}}