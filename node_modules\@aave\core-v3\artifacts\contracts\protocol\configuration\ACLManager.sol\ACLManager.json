{"_format": "hh-sol-artifact-1", "contractName": "ACLManager", "sourceName": "contracts/protocol/configuration/ACLManager.sol", "abi": [{"inputs": [{"internalType": "contract IPoolAddressesProvider", "name": "provider", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"inputs": [], "name": "ADDRESSES_PROVIDER", "outputs": [{"internalType": "contract IPoolAddressesProvider", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "ASSET_LISTING_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "BRIDGE_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "EMERGENCY_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "FLASH_BORROWER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "POOL_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "RISK_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "name": "addAssetListingAdmin", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "bridge", "type": "address"}], "name": "addBridge", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "name": "addEmergencyAdmin", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "borrower", "type": "address"}], "name": "addFlashBorrower", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "name": "addPoolAdmin", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "name": "addRiskAdmin", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "name": "isAssetListingAdmin", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "bridge", "type": "address"}], "name": "isBridge", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "name": "isEmergencyAdmin", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "borrower", "type": "address"}], "name": "isFlashBorrower", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "name": "isPoolAdmin", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "name": "isRiskAdmin", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "name": "removeAssetListingAdmin", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "bridge", "type": "address"}], "name": "removeBridge", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "name": "removeEmergencyAdmin", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "borrower", "type": "address"}], "name": "removeFlashBorrower", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "name": "removePoolAdmin", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "name": "removeRiskAdmin", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "bytes32", "name": "adminRole", "type": "bytes32"}], "name": "setRoleAdmin", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}