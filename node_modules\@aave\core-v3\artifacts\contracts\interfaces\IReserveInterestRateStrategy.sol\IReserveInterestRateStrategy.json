{"_format": "hh-sol-artifact-1", "contractName": "IReserveInterestRateStrategy", "sourceName": "contracts/interfaces/IReserveInterestRateStrategy.sol", "abi": [{"inputs": [{"components": [{"internalType": "uint256", "name": "unbacked", "type": "uint256"}, {"internalType": "uint256", "name": "liquidityAdded", "type": "uint256"}, {"internalType": "uint256", "name": "liquidityTaken", "type": "uint256"}, {"internalType": "uint256", "name": "totalStableDebt", "type": "uint256"}, {"internalType": "uint256", "name": "totalVariableDebt", "type": "uint256"}, {"internalType": "uint256", "name": "averageStableBorrowRate", "type": "uint256"}, {"internalType": "uint256", "name": "reserveFactor", "type": "uint256"}, {"internalType": "address", "name": "reserve", "type": "address"}, {"internalType": "address", "name": "aToken", "type": "address"}], "internalType": "struct DataTypes.CalculateInterestRatesParams", "name": "params", "type": "tuple"}], "name": "calculateInterestRates", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}