const hre = require('hardhat');
require('dotenv').config(); // Ensure dotenv is loaded

async function main() {
  const ProFlashArbUltimate = await hre.ethers.getContractFactory('ProFlashArbUltimate');

  console.log('Deploying ProFlashArbUltimate contract...');
  // The contract constructor now takes no arguments, as protocol addresses are integrated via library
  const contract = await ProFlashArbUltimate.deploy();

  await contract.deployed();
  console.log('ProFlashArbUltimate deployed to:', contract.address);

  // --- Post-deployment Configuration ---
  console.log('\nConfiguring deployed contract...');

  // Set up roles for a specific executor and guardian address
  // Replace these with the actual addresses of your bot and guardian wallets
  const [deployer] = await hre.ethers.getSigners();
  const executorAddress = process.env.EXECUTOR_ADDRESS || deployer.address; // Default to deployer
  const guardianAddress = process.env.GUARDIAN_ADDRESS || deployer.address; // Default to deployer

  console.log(`Granting EXECUTOR_ROLE to ${executorAddress}...`);
  const EXECUTOR_ROLE = await contract.EXECUTOR_ROLE();
  const tx1 = await contract.grantRole(EXECUTOR_ROLE, executorAddress);
  await tx1.wait();
  console.log(`EXECUTOR_ROLE granted to ${executorAddress}.`);

  console.log(`Granting GUARDIAN_ROLE to ${guardianAddress}...`);
  const GUARDIAN_ROLE = await contract.GUARDIAN_ROLE();
  const tx2 = await contract.grantRole(GUARDIAN_ROLE, guardianAddress);
  await tx2.wait();
  console.log(`GUARDIAN_ROLE granted to ${guardianAddress}.`);

  // Configure parameters (min profit, max gas, cooldown, max losses)
  console.log('Updating contract configuration...');
  const tx3 = await contract.updateConfig(
    50,  // 0.5% min profit (in basis points)
    200 * 10**9, // 200 gwei max gas price (in Wei)
    3600, // 1 hour cooldown (in seconds)
    3     // 3 max consecutive losses
  );
  await tx3.wait();
  console.log('Contract configuration updated.');

  // Whitelist common tokens (using constants from the contract, derived from PolygonMainnetAddresses)
  console.log('Whitelisting tokens...');
  const WMATIC_TOKEN_ADDRESS = await contract.WMATIC();
  const USDC_TOKEN_ADDRESS = await contract.USDC();
  const WETH_TOKEN_ADDRESS = await contract.WETH();

  const tx4 = await contract.updateTokenWhitelist(WMATIC_TOKEN_ADDRESS, true);
  await tx4.wait();
  const tx5 = await contract.updateTokenWhitelist(USDC_TOKEN_ADDRESS, true);
  await tx5.wait();
  const tx6 = await contract.updateTokenWhitelist(WETH_TOKEN_ADDRESS, true);
  await tx6.wait();
  console.log('Tokens whitelisted.');

  console.log('Contract setup complete!');
}

main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
