{"_format": "hh-sol-artifact-1", "contractName": "InitializableAdminUpgradeabilityProxy", "sourceName": "contracts/dependencies/openzeppelin/upgradeability/InitializableAdminUpgradeabilityProxy.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "previousAdmin", "type": "address"}, {"indexed": false, "internalType": "address", "name": "newAdmin", "type": "address"}], "name": "Admin<PERSON><PERSON>ed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "Upgraded", "type": "event"}, {"stateMutability": "payable", "type": "fallback"}, {"inputs": [], "name": "admin", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newAdmin", "type": "address"}], "name": "changeAdmin", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "implementation", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "logic", "type": "address"}, {"internalType": "address", "name": "admin", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "initialize", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_logic", "type": "address"}, {"internalType": "bytes", "name": "_data", "type": "bytes"}], "name": "initialize", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}], "name": "upgradeTo", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "upgradeToAndCall", "outputs": [], "stateMutability": "payable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}