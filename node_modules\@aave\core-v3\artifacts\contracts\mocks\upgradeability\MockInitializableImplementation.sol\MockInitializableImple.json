{"_format": "hh-sol-artifact-1", "contractName": "MockInitializableImple", "sourceName": "contracts/mocks/upgradeability/MockInitializableImplementation.sol", "abi": [{"inputs": [], "name": "REVISION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "val", "type": "uint256"}, {"internalType": "string", "name": "txt", "type": "string"}, {"internalType": "uint256[]", "name": "vals", "type": "uint256[]"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newValue", "type": "uint256"}], "name": "setValue", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newValue", "type": "uint256"}], "name": "setValueViaProxy", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "text", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "value", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "values", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "0x608060405234801561001057600080fd5b506004361061007d5760003560e01c80635dd216101161005b5780635dd21610146100b75780635e383d21146100cc578063d31f8b6b146100df578063dde43cba146100f257600080fd5b80631f1bd692146100825780633fa4f245146100a057806355241077146100b7575b600080fd5b61008a6100fa565b60405161009791906103c9565b60405180910390f35b6100a960345481565b604051908152602001610097565b6100ca6100c536600461043c565b603455565b005b6100a96100da36600461043c565b610188565b6100ca6100ed366004610553565b6101a9565b6100a9600181565b6035805461010790610631565b80601f016020809104026020016040519081016040528092919081815260200182805461013390610631565b80156101805780601f1061015557610100808354040283529160200191610180565b820191906000526020600020905b81548152906001019060200180831161016357829003601f168201915b505050505081565b6036818154811061019857600080fd5b600091825260209091200154905081565b6001805460ff16806101ba5750303b155b806101c6575060005481115b610256576040517f08c379a000000000000000000000000000000000000000000000000000000000815260206004820152602e60248201527f436f6e747261637420696e7374616e63652068617320616c726561647920626560448201527f656e20696e697469616c697a6564000000000000000000000000000000000000606482015260840160405180910390fd5b60015460ff1615801561029357600180547fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff00168117905560008290555b603485905583516102ab9060359060208701906102f6565b5082516102bf90603690602086019061037a565b5080156102ef57600180547fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff001690555b5050505050565b82805461030290610631565b90600052602060002090601f016020900481019282610324576000855561036a565b82601f1061033d57805160ff191683800117855561036a565b8280016001018555821561036a579182015b8281111561036a57825182559160200191906001019061034f565b506103769291506103b4565b5090565b82805482825590600052602060002090810192821561036a579160200282018281111561036a57825182559160200191906001019061034f565b5b8082111561037657600081556001016103b5565b600060208083528351808285015260005b818110156103f6578581018301518582016040015282016103da565b81811115610408576000604083870101525b50601f017fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe016929092016040019392505050565b60006020828403121561044e57600080fd5b5035919050565b7f4e487b7100000000000000000000000000000000000000000000000000000000600052604160045260246000fd5b604051601f82017fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe016810167ffffffffffffffff811182821017156104cb576104cb610455565b604052919050565b600082601f8301126104e457600080fd5b8135602067ffffffffffffffff82111561050057610500610455565b8160051b61050f828201610484565b928352848101820192828101908785111561052957600080fd5b83870192505b848310156105485782358252918301919083019061052f565b979650505050505050565b60008060006060848603121561056857600080fd5b8335925060208085013567ffffffffffffffff8082111561058857600080fd5b818701915087601f83011261059c57600080fd5b8135818111156105ae576105ae610455565b6105de847fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe0601f84011601610484565b81815289858386010111156105f257600080fd5b81858501868301376000918101909401529193506040860135918083111561061957600080fd5b5050610627868287016104d3565b9150509250925092565b600181811c9082168061064557607f821691505b6020821081141561067f577f4e487b7100000000000000000000000000000000000000000000000000000000600052602260045260246000fd5b5091905056fea2646970667358221220d3b6cd3addccaad77e50ddcd2359885d6a6513a49c0ed2c0635e1967b0a72b2064736f6c634300080a0033", "linkReferences": {}, "deployedLinkReferences": {}}