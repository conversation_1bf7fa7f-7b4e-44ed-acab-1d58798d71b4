import dotenv from 'dotenv';

dotenv.config({ path: '../../.env' }); // Adjust path to root .env file

interface AppConfig {
  GROQ_API_KEY: string;
  PRIVATE_KEY: string;
  RPC_URL: string;
  FLASHBOTS_AUTH_KEY: string;
  CONTRACT_ADDRESS: string;
  PORT: number;
  USDC_TOKEN_ADDRESS: string; // Add token addresses from contract
  WETH_TOKEN_ADDRESS: string;
  WMATIC_TOKEN_ADDRESS: string;
  PROFIT_WALLET_ADDRESS: string; // Add the new profit wallet address
}

const config: AppConfig = {
  GROQ_API_KEY: process.env.GROQ_API_KEY || '',
  PRIVATE_KEY: process.env.PRIVATE_KEY || '',
  RPC_URL: process.env.POLYGON_RPC_URL || '', // Use POLYGON_RPC_URL from root .env
  FLASHBOTS_AUTH_KEY: process.env.FLASHBOTS_AUTH_KEY || '',
  CONTRACT_ADDRESS: process.env.CONTRACT_ADDRESS || '',
  PORT: parseInt(process.env.PORT || '8080', 10),
  // These will be fetched from the contract or config after deployment
  USDC_TOKEN_ADDRESS: '******************************************', // Polygon Mainnet USDC
  WETH_TOKEN_ADDRESS: '******************************************', // Polygon Mainnet WETH
  WMATIC_TOKEN_ADDRESS: '******************************************', // Polygon Mainnet WMATIC
  PROFIT_WALLET_ADDRESS: process.env.PROFIT_WALLET || '', // Load the profit wallet address
};

// Basic validation
if (!config.GROQ_API_KEY) {
  console.warn('GROQ_API_KEY is not set in .env');
}
if (!config.PRIVATE_KEY) {
  console.warn('PRIVATE_KEY is not set in .env. The bot will not be able to sign transactions.');
}
if (!config.RPC_URL) {
  console.warn('RPC_URL is not set in .env. Blockchain interactions may fail.');
}
if (!config.CONTRACT_ADDRESS) {
  console.warn('CONTRACT_ADDRESS is not set in .env. Arbitrage executions will not work.');
}
if (!config.PROFIT_WALLET_ADDRESS) {
  console.warn('PROFIT_WALLET is not set in .env. Automatic withdrawals may not function correctly.');
}

export default config;
