{"_format": "hh-sol-artifact-1", "contractName": "IFlashLoanSimpleReceiver", "sourceName": "contracts/flashloan/interfaces/IFlashLoanSimpleReceiver.sol", "abi": [{"inputs": [], "name": "ADDRESSES_PROVIDER", "outputs": [{"internalType": "contract IPoolAddressesProvider", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "POOL", "outputs": [{"internalType": "contract IPool", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "premium", "type": "uint256"}, {"internalType": "address", "name": "initiator", "type": "address"}, {"internalType": "bytes", "name": "params", "type": "bytes"}], "name": "executeOperation", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}