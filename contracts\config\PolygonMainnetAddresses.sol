// File: contracts/config/PolygonMainnetAddresses.sol
// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

library PolygonMainnetAddresses {
    // AAVE V3 Addresses
    address public constant AAVE_V3_POOL = 0x794a61358D6845594F94dc1DB02A252b5b4814aD;
    address public constant AAVE_V3_POOL_ADDRESSES_PROVIDER = 0xa97684ead0e402dC232d5A977953DF7ECBaB3CDb;
    address public constant AAVE_V3_ORACLE = 0xb023e699F5a33916Ea823A16485e259257cA8Bd1;
    
    // Balancer V2 Addresses
    address public constant BALANCER_V2_VAULT = 0xBA12222222228d8Ba445958a75a0704d566BF2C8;
    address public constant BALANCER_V2_HELPERS = 0x239e55F427D44C3cc793f49bFB507ebe76638a2b;
    
    // Uniswap V3 Addresses
    address public constant UNISWAP_V3_FACTORY = 0x1F98431c8aD98523631AE4a59f267346ea31F984;
 address public constant UNISWAP_V3_ROUTER = 0xe592427a0AEcE92DE3eDEE1f18f0157Cc0aaF61d; // This is Router02, not Router
    address public constant UNISWAP_V3_QUOTER_V2 = 0x61fFE014bA17989E743c5F6cB21bF9697530B21e;
    address public constant UNISWAP_V3_POSITION_MANAGER = 0xC36442b4a4522E871399CD717aBDD847Ab11FE88;
    
    // QuickSwap V3 (Algebra) Addresses
    address public constant QUICKSWAP_V3_FACTORY = 0x411b0fAcC3489691f28ad58c47006AF5E3Ab3A28;
    address public constant QUICKSWAP_V3_ROUTER = 0xf5b509bB0909a69B1c207E495f687a596C168E12;
    address public constant QUICKSWAP_V3_QUOTER = 0xa15F0D7377B2A0C0c10db057f641beD21028FC89;
    
    // SushiSwap Addresses
    address public constant SUSHISWAP_V2_FACTORY = ******************************************;
    address public constant SUSHISWAP_V2_ROUTER = ******************************************;
    address public constant SUSHISWAP_TRIDENT_ROUTER = ******************************************;
    
    // Chainlink Price Feeds (Aggregators)
    address public constant CHAINLINK_MATIC_USD = ******************************************;
    address public constant CHAINLINK_ETH_USD = ******************************************;
    address public constant CHAINLINK_BTC_USD = ******************************************;
    address public constant CHAINLINK_USDC_USD = ******************************************;
    address public constant CHAINLINK_USDT_USD = ******************************************;
    
    // Common Token Addresses
    address public constant WMATIC = ******************************************;
    address public constant WETH = ******************************************;
 address public constant WBTC = ******************************************;
    address public constant USDC = ******************************************;
    address public constant USDT = ******************************************;
    address public constant DAI = ******************************************;
    
    // MEV Protection (Polygon specific)
    // Direct Flashbots Relay is not officially available on Polygon as it is on Ethereum.
    // MEV protection on Polygon often involves private RPCs, block builders, or custom solutions.
    // Setting these to address(0) to indicate non-direct integration or a placeholder.
    address public constant POLYGON_FLASHBOTS_RELAY = address(0);
    address public constant POLYGON_MEMPOOL_GUARDIAN = address(0); 
}
