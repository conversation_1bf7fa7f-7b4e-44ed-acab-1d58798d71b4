{"_format": "hh-sol-artifact-1", "contractName": "ICreditDelegationToken", "sourceName": "contracts/interfaces/ICreditDelegationToken.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "fromUser", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to<PERSON><PERSON>", "type": "address"}, {"indexed": true, "internalType": "address", "name": "asset", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "BorrowAllowanceDelegated", "type": "event"}, {"inputs": [{"internalType": "address", "name": "delegatee", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "approveDelegation", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "fromUser", "type": "address"}, {"internalType": "address", "name": "to<PERSON><PERSON>", "type": "address"}], "name": "borrowAllowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "delegator", "type": "address"}, {"internalType": "address", "name": "delegatee", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "delegationWithSig", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}