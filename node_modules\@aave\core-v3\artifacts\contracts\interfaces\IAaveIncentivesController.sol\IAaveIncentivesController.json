{"_format": "hh-sol-artifact-1", "contractName": "IAaveIncentivesController", "sourceName": "contracts/interfaces/IAaveIncentivesController.sol", "abi": [{"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "totalSupply", "type": "uint256"}, {"internalType": "uint256", "name": "userBalance", "type": "uint256"}], "name": "handleAction", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}