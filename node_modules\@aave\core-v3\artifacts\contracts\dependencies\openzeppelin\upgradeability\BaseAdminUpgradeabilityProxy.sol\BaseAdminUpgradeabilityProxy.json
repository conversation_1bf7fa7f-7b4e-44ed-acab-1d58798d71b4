{"_format": "hh-sol-artifact-1", "contractName": "BaseAdminUpgradeabilityProxy", "sourceName": "contracts/dependencies/openzeppelin/upgradeability/BaseAdminUpgradeabilityProxy.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "previousAdmin", "type": "address"}, {"indexed": false, "internalType": "address", "name": "newAdmin", "type": "address"}], "name": "Admin<PERSON><PERSON>ed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "Upgraded", "type": "event"}, {"stateMutability": "payable", "type": "fallback"}, {"inputs": [], "name": "admin", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newAdmin", "type": "address"}], "name": "changeAdmin", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "implementation", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}], "name": "upgradeTo", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "upgradeToAndCall", "outputs": [], "stateMutability": "payable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}