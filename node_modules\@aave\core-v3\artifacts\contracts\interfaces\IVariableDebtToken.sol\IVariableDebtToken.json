{"_format": "hh-sol-artifact-1", "contractName": "IVariableDebtToken", "sourceName": "contracts/interfaces/IVariableDebtToken.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "target", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "balanceIncrease", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "index", "type": "uint256"}], "name": "Burn", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "underlyingAsset", "type": "address"}, {"indexed": true, "internalType": "address", "name": "pool", "type": "address"}, {"indexed": false, "internalType": "address", "name": "incentivesController", "type": "address"}, {"indexed": false, "internalType": "uint8", "name": "debtTokenDecimals", "type": "uint8"}, {"indexed": false, "internalType": "string", "name": "debtTokenName", "type": "string"}, {"indexed": false, "internalType": "string", "name": "debtTokenSymbol", "type": "string"}, {"indexed": false, "internalType": "bytes", "name": "params", "type": "bytes"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "caller", "type": "address"}, {"indexed": true, "internalType": "address", "name": "onBehalfOf", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "balanceIncrease", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "index", "type": "uint256"}], "name": "Mint", "type": "event"}, {"inputs": [], "name": "UNDERLYING_ASSET_ADDRESS", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "burn", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getPreviousIndex", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getScaledUserBalanceAndSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "contract IPool", "name": "pool", "type": "address"}, {"internalType": "address", "name": "underlyingAsset", "type": "address"}, {"internalType": "contract IAaveIncentivesController", "name": "incentivesController", "type": "address"}, {"internalType": "uint8", "name": "debtTokenDecimals", "type": "uint8"}, {"internalType": "string", "name": "debtTokenName", "type": "string"}, {"internalType": "string", "name": "debtTokenSymbol", "type": "string"}, {"internalType": "bytes", "name": "params", "type": "bytes"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "address", "name": "onBehalfOf", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "mint", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "scaledBalanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "scaledTotalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}